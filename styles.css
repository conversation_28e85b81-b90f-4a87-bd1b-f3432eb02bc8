/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background-color: #f5f5f5;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

h1 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #000;
}

.subtitle {
    font-size: 14px;
    margin-bottom: 20px;
    color: #333;
}

/* Fieldset styles */
fieldset {
    border: 2px solid #999;
    border-radius: 5px;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #fafafa;
}

legend {
    font-weight: bold;
    padding: 0 10px;
    color: #000;
    font-size: 14px;
}

/* Form row styles */
.form-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 15px;
}

.form-row:last-child {
    margin-bottom: 0;
}

label {
    min-width: 140px;
    font-weight: normal;
    color: #000;
    padding-top: 3px;
    flex-shrink: 0;
}

/* Input styles */
input[type="text"],
textarea,
select {
    border: 1px solid #999;
    padding: 4px 6px;
    font-size: 13px;
    font-family: Arial, sans-serif;
    background-color: white;
}

input[type="text"] {
    width: 200px;
    height: 22px;
}

textarea {
    width: 200px;
    resize: vertical;
    font-family: Arial, sans-serif;
}

select {
    width: 200px;
    height: 22px;
    background-color: white;
}

select[multiple] {
    height: auto;
    padding: 2px;
}

select[size="4"] {
    height: 80px;
}

/* File input styles */
input[type="file"] {
    font-size: 12px;
    width: 200px;
}

/* Specific section adjustments */
.address-section {
    border-color: #666;
}

.details-section {
    border-color: #666;
}

.interests-section {
    border-color: #666;
}

/* Option styling for better visibility */
option {
    padding: 2px 4px;
}

/* Focus styles */
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #4A90E2;
    outline-offset: 1px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .form-row {
        flex-direction: column;
        gap: 5px;
    }
    
    label {
        min-width: auto;
    }
    
    input[type="text"],
    textarea,
    select {
        width: 100%;
    }
}
