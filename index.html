<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Juliet's Assignment</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Class Assignment (Exercise) 01</h1>
        <p class="subtitle">This should be completed by the end of the class practical period this week.</p>
        
        <form>
            <fieldset class="address-section">
                <legend>Your address</legend>
                <div class="form-row">
                    <label for="firstName">First name</label>
                    <input type="text" id="firstName" name="firstName">
                </div>
                <div class="form-row">
                    <label for="lastName">Last name</label>
                    <input type="text" id="lastName" name="lastName">
                </div>
                <div class="form-row">
                    <label for="street">Street</label>
                    <input type="text" id="street" name="street">
                </div>
                <div class="form-row">
                    <label for="city">City</label>
                    <input type="text" id="city" name="city">
                </div>
            </fieldset>

            <fieldset class="details-section">
                <legend>Additional details</legend>
                <div class="form-row">
                    <label for="biography">Biography</label>
                    <textarea id="biography" name="biography" rows="3"></textarea>
                </div>
                <div class="form-row">
                    <label for="image">Image</label>
                    <input type="file" id="image" name="image">
                </div>
                <div class="form-row">
                    <label for="ageGroup">Age Group</label>
                    <select id="ageGroup" name="ageGroup">
                        <option value="0-9">0 to 9 Years</option>
                        <option value="10-19">10 to 19 Years</option>
                        <option value="20-29">20 to 29 Years</option>
                        <option value="30-39">30 to 39 Years</option>
                        <option value="40-49">40 to 49 Years</option>
                        <option value="50+">50+ Years</option>
                    </select>
                </div>
            </fieldset>

            <fieldset class="interests-section">
                <legend>Interests</legend>
                <div class="form-row">
                    <label for="hobbies">Hobbies</label>
                    <select id="hobbies" name="hobbies" multiple size="4">
                        <option value="playing-soccer">Playing soccer</option>
                        <option value="dancing">Dancing</option>
                        <option value="gardening">Gardening</option>
                        <option value="watching-movies">Watching movies</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="favouriteCar">Favourite car?</label>
                    <select id="favouriteCar" name="favouriteCar">
                        <option value="volvo">Volvo</option>
                        <option value="bmw">BMW</option>
                        <option value="mercedes">Mercedes</option>
                        <option value="audi">Audi</option>
                        <option value="toyota">Toyota</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="favouriteTransport">Favourite public transport?</label>
                    <select id="favouriteTransport" name="favouriteTransport" multiple size="4">
                        <option value="ground"><b>Ground</b></option>
                        <option value="train">Train</option>
                        <option value="bus">Bus</option>
                        <option value="water">Water</option>
                    </select>
                </div>
            </fieldset>
        </form>
    </div>
</body>
</html>
